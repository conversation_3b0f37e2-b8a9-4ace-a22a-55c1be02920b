/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Path } from '../../util/Path';
import { NamedNode, Node } from '../Node';
import { Index } from './Index';
export declare class PathIndex extends Index {
    private indexPath_;
    constructor(indexPath_: Path);
    protected extractChild(snap: Node): Node;
    isDefinedOn(node: Node): boolean;
    compare(a: NamedNode, b: NamedNode): number;
    makePost(indexValue: object, name: string): NamedNode;
    maxPost(): NamedNode;
    toString(): string;
}
