{"version": 3, "file": "subchannel-pool.js", "sourceRoot": "", "sources": ["../../src/subchannel-pool.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;;GAeG;;;AA0JH,8CAMC;AA9JD,uDAAwE;AACxE,6CAA0C;AAC1C,6DAG8B;AAE9B,6CAAoD;AACpD,2CAAuD;AAEvD,uDAAuD;AACvD;;;GAGG;AACH,MAAM,kBAAkB,GAAG,KAAM,CAAC;AAElC,MAAa,cAAc;IAezB;;;OAGG;IACH;QAlBQ,SAAI,GAOR,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;QAExB;;WAEG;QACK,iBAAY,GAA0B,IAAI,CAAC;IAMpC,CAAC;IAEhB;;;OAGG;IACH,sBAAsB;QACpB,IAAI,qBAAqB,GAAG,IAAI,CAAC;QAEjC;;wCAEgC;QAChC,yCAAyC;QACzC,KAAK,MAAM,aAAa,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YACtC,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YAEpD,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,MAAM,CAChD,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,UAAU,CAAC,aAAa,EAAE,CAC3C,CAAC;YAEF,IAAI,gBAAgB,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,qBAAqB,GAAG,KAAK,CAAC;YAChC,CAAC;YAED;;kEAEsD;YACtD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,gBAAgB,CAAC;QAC9C,CAAC;QACD;8DACsD;QAEtD,gEAAgE;QAChE,IAAI,qBAAqB,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YACxD,aAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB;;QACf,IAAI,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;YAC/B,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC,GAAG,EAAE;gBACnC,IAAI,CAAC,sBAAsB,EAAE,CAAC;YAChC,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAEvB,mEAAmE;YACnE,kEAAkE;YAClE,MAAA,MAAA,IAAI,CAAC,YAAY,EAAC,KAAK,kDAAI,CAAC;QAC9B,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACH,qBAAqB,CACnB,gBAAyB,EACzB,gBAAmC,EACnC,gBAAgC,EAChC,kBAAsC;QAEtC,IAAI,CAAC,iBAAiB,EAAE,CAAC;QACzB,MAAM,aAAa,GAAG,IAAA,wBAAW,EAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,aAAa,IAAI,IAAI,CAAC,IAAI,EAAE,CAAC;YAC/B,MAAM,kBAAkB,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;YACpD,KAAK,MAAM,aAAa,IAAI,kBAAkB,EAAE,CAAC;gBAC/C,IACE,IAAA,2CAAsB,EACpB,gBAAgB,EAChB,aAAa,CAAC,iBAAiB,CAChC;oBACD,IAAA,qCAAmB,EACjB,gBAAgB,EAChB,aAAa,CAAC,gBAAgB,CAC/B;oBACD,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,kBAAkB,CAAC,EAC5D,CAAC;oBACD,OAAO,aAAa,CAAC,UAAU,CAAC;gBAClC,CAAC;YACH,CAAC;QACH,CAAC;QACD,mDAAmD;QACnD,MAAM,UAAU,GAAG,IAAI,uBAAU,CAC/B,gBAAgB,EAChB,gBAAgB,EAChB,gBAAgB,EAChB,kBAAkB,EAClB,IAAI,oCAAwB,CAAC,gBAAgB,CAAC,CAC/C,CAAC;QACF,IAAI,CAAC,CAAC,aAAa,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,EAAE,CAAC;QAChC,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC;YAC5B,iBAAiB,EAAE,gBAAgB;YACnC,gBAAgB;YAChB,kBAAkB;YAClB,UAAU;SACX,CAAC,CAAC;QACH,UAAU,CAAC,GAAG,EAAE,CAAC;QACjB,OAAO,UAAU,CAAC;IACpB,CAAC;CACF;AA/HD,wCA+HC;AAED,MAAM,oBAAoB,GAAG,IAAI,cAAc,EAAE,CAAC;AAElD;;;GAGG;AACH,SAAgB,iBAAiB,CAAC,MAAe;IAC/C,IAAI,MAAM,EAAE,CAAC;QACX,OAAO,oBAAoB,CAAC;IAC9B,CAAC;SAAM,CAAC;QACN,OAAO,IAAI,cAAc,EAAE,CAAC;IAC9B,CAAC;AACH,CAAC"}