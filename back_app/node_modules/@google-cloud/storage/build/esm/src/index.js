// Copyright 2019 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
/**
 * The `@google-cloud/storage` package has a single named export which is the
 * {@link Storage} (ES6) class, which should be instantiated with `new`.
 *
 * See {@link Storage} and {@link ClientConfig} for client methods and
 * configuration options.
 *
 * @module {Storage} @google-cloud/storage
 * @alias nodejs-storage
 *
 * @example
 * Install the client library with <a href="https://www.npmjs.com/">npm</a>:
 * ```
 * npm install --save @google-cloud/storage
 * ```
 *
 * @example
 * Import the client library
 * ```
 * const {Storage} = require('@google-cloud/storage');
 * ```
 *
 * @example
 * Create a client that uses <a
 * href="https://cloud.google.com/docs/authentication/production#providing_credentials_to_your_application">Application
 * Default Credentials (ADC)</a>:
 * ```
 * const storage = new Storage();
 * ```
 *
 * @example
 * Create a client with <a
 * href="https://cloud.google.com/docs/authentication/production#obtaining_and_providing_service_account_credentials_manually">explicit
 * credentials</a>:
 * ```
 * const storage = new Storage({ projectId:
 * 'your-project-id', keyFilename: '/path/to/keyfile.json'
 * });
 * ```
 *
 * @example <caption>include:samples/quickstart.js</caption>
 * region_tag:storage_quickstart
 * Full quickstart example:
 */
export { ApiError } from './nodejs-common/index.js';
export { IdempotencyStrategy, RETRYABLE_ERR_FN_DEFAULT, Storage, } from './storage.js';
export { Bucket, } from './bucket.js';
export * from './crc32c.js';
export { Channel } from './channel.js';
export { File, } from './file.js';
export * from './hash-stream-validator.js';
export { HmacKey, } from './hmacKey.js';
export { Iam, } from './iam.js';
export { Notification, } from './notification.js';
export * from './transfer-manager.js';
